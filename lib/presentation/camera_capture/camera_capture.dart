import 'package:camera/camera.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/app_bottom_navigation_widget.dart';
import './widgets/camera_controls_widget.dart';
import './widgets/camera_preview_widget.dart';
import './widgets/processing_overlay_widget.dart';
import './widgets/scanning_overlay_widget.dart';
import './widgets/scanning_tips_widget.dart';

class CameraCapture extends StatefulWidget {
  const CameraCapture({super.key});

  @override
  State<CameraCapture> createState() => _CameraCaptureState();
}

class _CameraCaptureState extends State<CameraCapture>
    with WidgetsBindingObserver, TickerProviderStateMixin {
  // Camera related variables
  CameraController? _cameraController;
  List<CameraDescription> _cameras = [];
  bool _isCameraInitialized = false;
  bool _isProcessing = false;
  FlashMode _currentFlashMode = FlashMode.auto;
  int _currentIndex = 3; // Camera tab index

  // Scanning state variables
  bool _isScanning = false;
  double _confidence = 0.0;
  bool _showTips = true;
  double _processingProgress = 0.0;

  // Image picker
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
    _startScanningSimulation();
    _hideTipsAfterDelay();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cameraController?.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    if (state == AppLifecycleState.inactive) {
      _cameraController?.dispose();
    } else if (state == AppLifecycleState.resumed) {
      _initializeCamera();
    }
  }

  Future<bool> _requestCameraPermission() async {
    if (kIsWeb) return true;

    final status = await Permission.camera.request();
    return status.isGranted;
  }

  Future<void> _initializeCamera() async {
    try {
      if (!await _requestCameraPermission()) {
        _showPermissionDialog();
        return;
      }

      _cameras = await availableCameras();
      if (_cameras.isEmpty) return;

      final camera = kIsWeb
          ? _cameras.firstWhere(
              (c) => c.lensDirection == CameraLensDirection.front,
              orElse: () => _cameras.first)
          : _cameras.firstWhere(
              (c) => c.lensDirection == CameraLensDirection.back,
              orElse: () => _cameras.first);

      _cameraController = CameraController(
        camera,
        kIsWeb ? ResolutionPreset.medium : ResolutionPreset.high,
        enableAudio: false,
      );

      await _cameraController!.initialize();
      await _applySettings();

      if (mounted) {
        setState(() {
          _isCameraInitialized = true;
        });
      }
    } catch (e) {
      debugPrint('Camera initialization error: \$e');
      if (mounted) {
        _showErrorSnackBar('Camera initialization failed. Please try again.');
      }
    }
  }

  Future<void> _applySettings() async {
    if (_cameraController == null) return;

    try {
      await _cameraController!.setFocusMode(FocusMode.auto);
      if (!kIsWeb) {
        await _cameraController!.setFlashMode(_currentFlashMode);
      }
    } catch (e) {
      debugPrint('Settings application error: \$e');
    }
  }

  void _startScanningSimulation() {
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isScanning = true;
        });
        _updateConfidence();
      }
    });
  }

  void _updateConfidence() {
    if (!_isScanning) return;

    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _isScanning) {
        setState(() {
          _confidence = (_confidence + 0.1).clamp(0.0, 0.9);
        });
        if (_confidence < 0.9) {
          _updateConfidence();
        }
      }
    });
  }

  void _hideTipsAfterDelay() {
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _showTips = false;
        });
      }
    });
  }

  Future<void> _capturePhoto() async {
    if (_cameraController == null ||
        !_cameraController!.value.isInitialized ||
        _isProcessing) {
      return;
    }

    try {
      HapticFeedback.mediumImpact();

      setState(() {
        _isProcessing = true;
        _processingProgress = 0.0;
      });

      final XFile photo = await _cameraController!.takePicture();
      debugPrint('Photo captured: ${photo.path}');

      // Navigate to photo preview screen
      if (mounted) {
        Navigator.pushNamed(
          context,
          '/photo-preview',
          arguments: {
            'imagePath': photo.path,
            'source': 'camera',
          },
        );
      }
    } catch (e) {
      debugPrint('Photo capture error: $e');
      _showErrorSnackBar('Failed to capture photo. Please try again.');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _processingProgress = 0.0;
        });
      }
    }
  }



  Future<void> _openGallery() async {
    if (_isProcessing) return;

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        HapticFeedback.selectionClick();
        debugPrint('Gallery image selected: ${image.path}');

        // Navigate to photo preview screen
        if (mounted) {
          Navigator.pushNamed(
            context,
            '/photo-preview',
            arguments: {
              'imagePath': image.path,
              'source': 'gallery',
            },
          );
        }
      }
    } catch (e) {
      debugPrint('Gallery selection error: $e');
      _showErrorSnackBar('Failed to select image from gallery. Please try again.');
    }
  }

  Future<void> _toggleFlash() async {
    if (_cameraController == null || kIsWeb) return;

    try {
      FlashMode newMode;
      switch (_currentFlashMode) {
        case FlashMode.off:
          newMode = FlashMode.auto;
          break;
        case FlashMode.auto:
          newMode = FlashMode.always;
          break;
        case FlashMode.always:
          newMode = FlashMode.off;
          break;
        default:
          newMode = FlashMode.auto;
      }

      await _cameraController!.setFlashMode(newMode);
      HapticFeedback.selectionClick();

      setState(() {
        _currentFlashMode = newMode;
      });
    } catch (e) {
      debugPrint('Flash toggle error: \$e');
    }
  }

  void _onTapToFocus() {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      _cameraController!.setFocusMode(FocusMode.auto);
      HapticFeedback.selectionClick();
    } catch (e) {
      debugPrint('Focus error: \$e');
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Camera Permission Required',
          style: AppTheme.lightTheme.textTheme.titleLarge,
        ),
        content: Text(
          'FloraVision needs camera access to identify plants. Please grant camera permission in your device settings.',
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.lightTheme.colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });

    // Navigate to different screens based on tab
    switch (index) {
      case 0:
        Navigator.pushReplacementNamed(context, '/splash-screen');
        break;
      case 1:
        Navigator.pushReplacementNamed(context, '/home-dashboard');
        break;
      case 2:
        Navigator.pushReplacementNamed(context, '/plant-health-diagnosis');
        break;
      case 3:
        // Current screen - Camera Capture
        break;
      case 4:
        Navigator.pushReplacementNamed(context, '/my-plant-collection');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Camera preview
          CameraPreviewWidget(
            cameraController: _cameraController,
            isInitialized: _isCameraInitialized,
            onTapToFocus: _onTapToFocus,
          ),

          // Scanning overlay
          if (_isCameraInitialized && !_isProcessing)
            ScanningOverlayWidget(
              isScanning: _isScanning,
              confidence: _confidence,
            ),

          // Top scanning tips
          if (_isCameraInitialized && !_isProcessing)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: ScanningTipsWidget(
                isVisible: _showTips,
              ),
            ),

          // Bottom camera controls
          if (_isCameraInitialized && !_isProcessing)
            Positioned(
              bottom: 10.h, // Space for bottom navigation
              left: 0,
              right: 0,
              child: CameraControlsWidget(
                onCapturePhoto: _capturePhoto,
                onOpenGallery: _openGallery,
                onToggleFlash: _toggleFlash,
                currentFlashMode: _currentFlashMode,
                isProcessing: _isProcessing,
              ),
            ),

          // Processing overlay
          ProcessingOverlayWidget(
            isVisible: _isProcessing,
            progress: _processingProgress,
          ),

          // Back button
          if (!_isProcessing)
            Positioned(
              top: MediaQuery.of(context).padding.top + 2.h,
              left: 4.w,
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: CustomIconWidget(
                      iconName: 'arrow_back',
                      color: AppTheme.lightTheme.colorScheme.surface,
                      size: 24,
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: _isProcessing
          ? null
          : AppBottomNavigationWidget(
              currentIndex: _currentIndex,
              onTap: _onTabTapped,
            ),
    );
  }
}
