import 'package:flutter/material.dart';

import '../core/app_export.dart';

class AppBottomNavigationWidget extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const AppBottomNavigationWidget({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: onTap,
      type: AppTheme.lightTheme.bottomNavigationBarTheme.type,
      backgroundColor:
          AppTheme.lightTheme.bottomNavigationBarTheme.backgroundColor,
      selectedItemColor:
          AppTheme.lightTheme.bottomNavigationBarTheme.selectedItemColor,
      unselectedItemColor:
          AppTheme.lightTheme.bottomNavigationBarTheme.unselectedItemColor,
      elevation: AppTheme.lightTheme.bottomNavigationBarTheme.elevation,
      items: [
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'home',
            color: currentIndex == 0
                ? AppTheme
                    .lightTheme.bottomNavigationBarTheme.selectedItemColor!
                : AppTheme
                    .lightTheme.bottomNavigationBarTheme.unselectedItemColor!,
            size: 24,
          ),
          label: 'Home',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'dashboard',
            color: currentIndex == 1
                ? AppTheme
                    .lightTheme.bottomNavigationBarTheme.selectedItemColor!
                : AppTheme
                    .lightTheme.bottomNavigationBarTheme.unselectedItemColor!,
            size: 24,
          ),
          label: 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'medical_services',
            color: currentIndex == 2
                ? AppTheme
                    .lightTheme.bottomNavigationBarTheme.selectedItemColor!
                : AppTheme
                    .lightTheme.bottomNavigationBarTheme.unselectedItemColor!,
            size: 24,
          ),
          label: 'Diagnosis',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'camera_alt',
            color: currentIndex == 3
                ? AppTheme
                    .lightTheme.bottomNavigationBarTheme.selectedItemColor!
                : AppTheme
                    .lightTheme.bottomNavigationBarTheme.unselectedItemColor!,
            size: 24,
          ),
          label: 'Camera',
        ),
        BottomNavigationBarItem(
          icon: CustomIconWidget(
            iconName: 'collections',
            color: currentIndex == 4
                ? AppTheme
                    .lightTheme.bottomNavigationBarTheme.selectedItemColor!
                : AppTheme
                    .lightTheme.bottomNavigationBarTheme.unselectedItemColor!,
            size: 24,
          ),
          label: 'Collection',
        ),
      ],
    );
  }
}
